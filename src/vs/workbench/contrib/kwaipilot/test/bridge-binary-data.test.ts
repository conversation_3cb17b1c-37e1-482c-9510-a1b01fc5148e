/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as assert from 'assert';
import { SerializableObjectWithBuffers } from '../../../services/extensions/common/proxyIdentifier.js';
import { VSBuffer } from '../../../../base/common/buffer.js';

suite('Bridge Binary Data Transfer', () => {

	test('should preserve ArrayBuffer data', () => {
		// 创建测试数据
		const originalBuffer = new ArrayBuffer(10);
		const view = new Uint8Array(originalBuffer);
		for (let i = 0; i < 10; i++) {
			view[i] = i;
		}

		const testData = {
			text: 'hello world',
			buffer: originalBuffer,
			typedArray: new Uint8Array([1, 2, 3, 4, 5])
		};

		// 模拟转换过程
		const convertedData = convertArrayBuffersToVSBuffers(testData);
		const wrappedData = new SerializableObjectWithBuffers(convertedData);

		// 模拟传输后的恢复过程
		const restoredData = restoreVSBuffersToArrayBuffers(wrappedData.value);

		// 验证数据完整性
		assert.strictEqual(restoredData.text, 'hello world');
		assert.ok(restoredData.buffer instanceof ArrayBuffer);
		assert.strictEqual(restoredData.buffer.byteLength, 10);

		const restoredView = new Uint8Array(restoredData.buffer);
		for (let i = 0; i < 10; i++) {
			assert.strictEqual(restoredView[i], i);
		}

		assert.ok(restoredData.typedArray instanceof Uint8Array);
		assert.strictEqual(restoredData.typedArray.length, 5);
		for (let i = 0; i < 5; i++) {
			assert.strictEqual(restoredData.typedArray[i], i + 1);
		}
	});

	test('should handle nested objects with binary data', () => {
		const testData = {
			nested: {
				buffer: new ArrayBuffer(5),
				array: [new Uint8Array([10, 20, 30])]
			}
		};

		const convertedData = convertArrayBuffersToVSBuffers(testData);
		const restoredData = restoreVSBuffersToArrayBuffers(convertedData);

		assert.ok(restoredData.nested.buffer instanceof ArrayBuffer);
		assert.strictEqual(restoredData.nested.buffer.byteLength, 5);
		assert.ok(restoredData.nested.array[0] instanceof Uint8Array);
		assert.strictEqual(restoredData.nested.array[0].length, 3);
	});

	// 辅助函数 - 复制自 BridgeImpl.ts 的逻辑
	function convertArrayBuffersToVSBuffers(obj: any): any {
		if (obj instanceof ArrayBuffer) {
			return VSBuffer.wrap(new Uint8Array(obj));
		}
		if (ArrayBuffer.isView(obj)) {
			return {
				$$vscode_typed_array$$: true,
				type: obj.constructor.name,
				buffer: VSBuffer.wrap(new Uint8Array(obj.buffer, obj.byteOffset, obj.byteLength)),
				byteOffset: obj.byteOffset,
				byteLength: obj.byteLength
			};
		}
		if (obj && typeof obj === 'object') {
			const result: any = {};
			for (const key in obj) {
				if (obj.hasOwnProperty(key)) {
					result[key] = convertArrayBuffersToVSBuffers(obj[key]);
				}
			}
			return result;
		}
		if (Array.isArray(obj)) {
			return obj.map(item => convertArrayBuffersToVSBuffers(item));
		}
		return obj;
	}

	function restoreVSBuffersToArrayBuffers(obj: any): any {
		if (obj && obj.$$vscode_typed_array$$) {
			const TypedArrayConstructor = (globalThis as any)[obj.type];
			if (TypedArrayConstructor && obj.buffer && obj.buffer.buffer) {
				return new TypedArrayConstructor(obj.buffer.buffer, obj.byteOffset, obj.byteLength / TypedArrayConstructor.BYTES_PER_ELEMENT);
			}
		}
		if (obj instanceof VSBuffer) {
			return obj.buffer.buffer.slice(obj.buffer.byteOffset, obj.buffer.byteOffset + obj.buffer.byteLength);
		}
		if (obj && typeof obj === 'object') {
			const result: any = {};
			for (const key in obj) {
				if (obj.hasOwnProperty(key)) {
					result[key] = restoreVSBuffersToArrayBuffers(obj[key]);
				}
			}
			return result;
		}
		if (Array.isArray(obj)) {
			return obj.map(item => restoreVSBuffersToArrayBuffers(item));
		}
		return obj;
	}
});
