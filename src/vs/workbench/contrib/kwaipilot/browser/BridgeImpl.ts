import { ICommandService, CommandsRegistry } from '../../../../platform/commands/common/commands.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import {
	IBridgeService,
	BridgeMessage,
	CallbackFunction,
	HandlerFunction,
	MessageListener,
	IKwaiPilotBridgeChannel
} from '../common/bridgeService.js';
import { SerializableObjectWithBuffers } from '../../../services/extensions/common/proxyIdentifier.js';
import { VSBuffer } from '../../../../base/common/buffer.js';

/**
 * Bridge 服务实现
 * 提供 WebView 和 Extension 之间的基础桥接通信功能
 * 使用 IPC 通道替代 executeCommand 以支持二进制数据传输
 */
export class BridgeService extends Disposable implements IBridgeService {
	readonly _serviceBrand: undefined;

	private callbacks: Map<number, CallbackFunction>;
	private handlers: Map<string, HandlerFunction>;
	private callbackId: number;
	private messageListeners: Set<MessageListener>;
	private bridgeChannel: IKwaiPilotBridgeChannel | undefined;

	constructor(
		@ICommandService private readonly commandService: ICommandService,
	) {
		super();

		this.callbacks = new Map();
		this.handlers = new Map();
		this.callbackId = 0;
		this.messageListeners = new Set();

		// 初始化 IPC 通道
		this.initializeBridgeChannel();

		// 保留原有的命令注册作为回退方案
		this._register(CommandsRegistry.registerCommand({
			id: `kwaipilot.bridge.postMessageFromExtension`,
			handler: async (_, message: BridgeMessage) => {
				if (message.protocol === 'callback') {
					const callback = this.callbacks.get(message.callbackId!);
					if (callback) {
						callback(message.data);
						this.callbacks.delete(message.callbackId!);
					}
				}
				else if (message.protocol === 'callHandler') {
					const handler = this.handlers.get(message.name!);
					if (handler) {
						const response = await handler(message.data);
						this.sendToExtension({
							protocol: 'callback',
							callbackId: message.callbackId,
							data: response,
						});
					}
				}
				else if (message.protocol === 'message') {
					this.messageListeners.forEach((listener) => {
						listener(message);
					});
				}
			}
		}));
	}

	private async initializeBridgeChannel(): Promise<void> {
		try {
			// 这里需要获取到插件进程的 IPC 连接
			// 具体实现取决于如何建立渲染进程到插件进程的连接
			// 暂时留空，后续实现
		} catch (error) {
			console.warn('Failed to initialize bridge channel, falling back to command-based communication', error);
		}
	}

	private sendToExtension(message: BridgeMessage): void {
		// 检查数据中是否包含 ArrayBuffer 或 TypedArray
		const processedMessage = this.processMessageForSerialization(message);

		if (this.bridgeChannel) {
			// 使用 IPC 通道发送，支持二进制数据
			if (message.protocol === 'callHandler') {
				const messageData = processedMessage instanceof SerializableObjectWithBuffers
					? processedMessage.value.data
					: processedMessage.data;
				this.bridgeChannel.callHandler(message.name!, messageData).then(response => {
					const callback = this.callbacks.get(message.callbackId!);
					if (callback) {
						callback(response);
						this.callbacks.delete(message.callbackId!);
					}
				}).catch(error => {
					console.error('Bridge channel call failed:', error);
				});
			} else {
				this.bridgeChannel.postMessage(processedMessage);
			}
		} else {
			// 使用 SerializableObjectWithBuffers 包装包含二进制数据的消息
			this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', processedMessage);
		}
	}

	private processMessageForSerialization(message: BridgeMessage): BridgeMessage | SerializableObjectWithBuffers<BridgeMessage> {
		// 检查消息中是否包含 ArrayBuffer 或 TypedArray
		if (this.containsBinaryData(message)) {
			// 将 ArrayBuffer 转换为 VSBuffer 并使用 SerializableObjectWithBuffers 包装
			const processedMessage = this.convertArrayBuffersToVSBuffers(message);
			return new SerializableObjectWithBuffers(processedMessage);
		}
		return message;
	}

	private containsBinaryData(obj: any): boolean {
		if (obj instanceof ArrayBuffer || ArrayBuffer.isView(obj)) {
			return true;
		}
		if (obj && typeof obj === 'object') {
			for (const key in obj) {
				if (obj.hasOwnProperty(key) && this.containsBinaryData(obj[key])) {
					return true;
				}
			}
		}
		if (Array.isArray(obj)) {
			return obj.some(item => this.containsBinaryData(item));
		}
		return false;
	}

	private convertArrayBuffersToVSBuffers(obj: any): any {
		if (obj instanceof ArrayBuffer) {
			return VSBuffer.wrap(new Uint8Array(obj));
		}
		if (ArrayBuffer.isView(obj)) {
			// 保存 TypedArray 的类型信息
			return {
				$$vscode_typed_array$$: true,
				type: obj.constructor.name,
				buffer: VSBuffer.wrap(new Uint8Array(obj.buffer, obj.byteOffset, obj.byteLength)),
				byteOffset: obj.byteOffset,
				byteLength: obj.byteLength
			};
		}
		if (obj && typeof obj === 'object') {
			const result: any = {};
			for (const key in obj) {
				if (obj.hasOwnProperty(key)) {
					result[key] = this.convertArrayBuffersToVSBuffers(obj[key]);
				}
			}
			return result;
		}
		if (Array.isArray(obj)) {
			return obj.map(item => this.convertArrayBuffersToVSBuffers(item));
		}
		return obj;
	}

	callHandler(handlerName: string, data: any, callback?: CallbackFunction): void {
		const callbackId = this.callbackId++;
		if (callback) {
			this.callbacks.set(callbackId, callback);
		}

		this.sendToExtension({
			protocol: 'callHandler',
			name: handlerName,
			callbackId,
			data,
		});
	}

	registerHandler(name: string, handler: HandlerFunction): void {
		this.handlers.set(name, handler);
	}

	reigisterBridgeCommand(commandName: string, onHandler: HandlerFunction): void {
		this._register(CommandsRegistry.registerCommand({
			id: `kwaipilot.bridge.${commandName}`,
			handler: (_, message: BridgeMessage) => {
				return onHandler(message);
			}
		}));
	}

	postMessage(message: any): void {
		this.sendToExtension({
			protocol: 'message',
			data: message,
		});
	}

	addMessageListener(listener: MessageListener): void {
		this.messageListeners.add(listener);
	}

	removeMessageListener(listener: MessageListener): void {
		this.messageListeners.delete(listener);
	}

	/**
	 * 清理资源
	 */
	override dispose(): void {
		this.callbacks.clear();
		this.handlers.clear();
		this.messageListeners.clear();
		super.dispose();
	}
}

// 注册为单例服务
registerSingleton(IBridgeService, BridgeService, InstantiationType.Delayed);
