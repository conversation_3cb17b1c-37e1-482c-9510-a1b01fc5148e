import { ICommandService, CommandsRegistry } from '../../../../platform/commands/common/commands.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import {
	IBridgeService,
	BridgeMessage,
	CallbackFunction,
	HandlerFunction,
	MessageListener
} from '../common/bridgeService.js';
import { IExtensionService } from '../../../services/extensions/common/extensions.js';
import { KwaiPilotBridgeClientService } from '../common/kwaiPilotIpc.js';

/**
 * Bridge 服务实现
 * 提供 WebView 和 Extension 之间的基础桥接通信功能
 * 使用单例模式管理全局桥接状态
 */
export class BridgeService extends Disposable implements IBridgeService {
	readonly _serviceBrand: undefined;

	private callbacks: Map<number, CallbackFunction>;
	private handlers: Map<string, HandlerFunction>;
	private callbackId: number;
	private messageListeners: Set<MessageListener>;
	private ipcService: KwaiPilotBridgeClientService | undefined;

	constructor(
		@ICommandService private readonly commandService: ICommandService,
		@IExtensionService private readonly extensionService: IExtensionService
	) {
		super();

		this.callbacks = new Map();
		this.handlers = new Map();
		this.callbackId = 0;
		this.messageListeners = new Set();

		// 初始化 IPC 连接
		this.initializeIpcConnection();

		// 注册桥接命令
		this._register(CommandsRegistry.registerCommand({
			id: `kwaipilot.bridge.postMessageFromExtension`,
			handler: async (accessor, message: BridgeMessage) => {
				if (message.protocol === 'callback') {
					const callback = this.callbacks.get(message.callbackId!);
					if (callback) {
						callback(message.data);
						this.callbacks.delete(message.callbackId!);
					}
				}
				else if (message.protocol === 'callHandler') {
					const handler = this.handlers.get(message.name!);
					if (handler) {
						const response = await handler(message.data);
						this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', {
							protocol: 'callback',
							callbackId: message.callbackId,
							data: response,
						});
					}
				}
				else if (message.protocol === 'message') {
					this.messageListeners.forEach((listener) => {
						listener(message);
					});
				}
			}
		}));
	}

	private async initializeIpcConnection(): Promise<void> {
		try {
			// 等待扩展服务准备就绪
			await this.extensionService.whenInstalledExtensionsRegistered();

			// 获取扩展主机管理器
			const extensionService = this.extensionService as any;
			const extensionHostManagers = extensionService._extensionHostManagers;

			if (extensionHostManagers && extensionHostManagers.length > 0) {
				const extensionHostManager = extensionHostManagers[0];
				const proxy = await extensionHostManager._proxy;

				if (proxy && proxy._rpcProtocol) {
					const channel = proxy._rpcProtocol.getChannel('kwaiPilotBridge');
					this.ipcService = new KwaiPilotBridgeClientService(channel);

					// 监听来自插件进程的消息
					this.ipcService.onMessage(message => {
						this.handleMessageFromExtension(message);
					});

					console.log('KwaiPilot IPC connection established');
				}
			}
		} catch (error) {
			console.warn('Failed to initialize IPC connection:', error);
		}
	}

	private handleMessageFromExtension(message: any): void {
		// 处理来自插件进程的消息
		if (message.protocol === 'callback') {
			const callback = this.callbacks.get(message.callbackId!);
			if (callback) {
				callback(message.data);
				this.callbacks.delete(message.callbackId!);
			}
		} else if (message.protocol === 'message') {
			this.messageListeners.forEach((listener) => {
				listener(message);
			});
		}
	}

	callHandler(handlerName: string, data: any, callback?: CallbackFunction): void {
		if (this.ipcService) {
			// 使用 IPC 通道 - 支持二进制数据
			this.ipcService.callHandler(handlerName, data)
				.then(response => callback?.(response))
				.catch(error => {
					console.error('IPC call failed:', error);
					// 回退到命令方式
					this.fallbackToCommand(handlerName, data, callback);
				});
		} else {
			// 回退到命令方式
			this.fallbackToCommand(handlerName, data, callback);
		}
	}

	private fallbackToCommand(handlerName: string, data: any, callback?: CallbackFunction): void {
		const callbackId = this.callbackId++;
		if (callback) {
			this.callbacks.set(callbackId, callback);
		}

		this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', {
			protocol: 'callHandler',
			name: handlerName,
			callbackId,
			data,
		});
	}

	registerHandler(name: string, handler: HandlerFunction): void {
		this.handlers.set(name, handler);
	}

	reigisterBridgeCommand(commandName: string, onHandler: HandlerFunction): void {
		this._register(CommandsRegistry.registerCommand({
			id: `kwaipilot.bridge.${commandName}`,
			handler: (accessor, message: BridgeMessage) => {
				return onHandler(message);
			}
		}));
	}

	postMessage(message: any): void {
		if (this.ipcService) {
			// 使用 IPC 通道
			this.ipcService.postMessage(message).catch(error => {
				console.error('IPC postMessage failed:', error);
		// 回退到命令方式
				this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', {
					protocol: 'message',
					data: message,
				});
			});
		} else {
			// 回退到命令方式
			this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', {
				protocol: 'message',
				data: message,
			});
		}
	}

	addMessageListener(listener: MessageListener): void {
		this.messageListeners.add(listener);
	}

	removeMessageListener(listener: MessageListener): void {
		this.messageListeners.delete(listener);
	}

	/**
	 * 清理资源
	 */
	override dispose(): void {
		this.callbacks.clear();
		this.handlers.clear();
		this.messageListeners.clear();
		super.dispose();
	}
}

// 注册为单例服务
registerSingleton(IBridgeService, BridgeService, InstantiationType.Delayed);
