/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Event, Emitter } from '../../../../base/common/event.js';
import { IChannel, IServerChannel } from '../../../../base/parts/ipc/common/ipc.js';

export interface IKwaiPilotBridgeService {
	callHandler(handlerName: string, data: any): Promise<any>;
	postMessage(message: any): Promise<void>;
	onMessage: Event<any>;
}

/**
 * IPC 客户端服务 - 运行在渲染进程中
 */
export class KwaiPilotBridgeClientService implements IKwaiPilotBridgeService {
	readonly onMessage: Event<any>;

	constructor(private channel: IChannel) {
		this.onMessage = this.channel.listen<any>('onMessage');
	}

	async callHandler(handlerName: string, data: any): Promise<any> {
		const callbackId = Math.random().toString(36).substring(2, 11);
		return this.channel.call('callHandler', { handlerName, data, callbackId });
	}

	async postMessage(message: any): Promise<void> {
		return this.channel.call('postMessage', message);
	}
}

/**
 * IPC 服务端通道 - 运行在插件进程中
 */
export class KwaiPilotBridgeServerChannel implements IServerChannel {
	private readonly _onMessage = new Emitter<any>();
	readonly onMessage: Event<any> = this._onMessage.event;

	constructor(private bridgeInstance: any) {}

	listen(_: unknown, event: string): Event<any> {
		switch (event) {
			case 'onMessage': return this.onMessage;
		}
		throw new Error(`Event not found: ${event}`);
	}

	async call(_: unknown, command: string, arg?: any): Promise<any> {
		switch (command) {
			case 'callHandler': {
				const { handlerName, data, callbackId } = arg;

				return new Promise((resolve, reject) => {
					try {
						// 调用现有的 Bridge 处理逻辑
						this.bridgeInstance.callNativeHandler(
							null, // webview 在 IPC 模式下可以为 null
							handlerName,
							data,
							callbackId,
							(response: any) => resolve(response)
						);
					} catch (error) {
						reject(error);
					}
				});
			}

			case 'postMessage': {
				const message = arg;
				this.bridgeInstance.handleOneWayMessage(null, message);
				return;
			}
		}

		throw new Error(`Call not found: ${command}`);
	}

	sendMessageToRenderer(message: any): void {
		this._onMessage.fire(message);
	}

	dispose(): void {
		this._onMessage.dispose();
	}
}
