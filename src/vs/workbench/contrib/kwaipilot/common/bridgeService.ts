import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { Event } from '../../../../base/common/event.js';

// 定义消息类型
export interface BridgeMessage {
	protocol: 'callback' | 'callHandler' | 'message';
	callbackId?: number;
	name?: string;
	data?: any;
}

// 定义回调函数类型
export type CallbackFunction = (data: any) => void;
export type HandlerFunction = (data: any) => Promise<any>;
export type MessageListener = (message: BridgeMessage) => void;

// 定义 IPC 通道接口
export interface IKwaiPilotBridgeChannel {
	/**
	 * 从渲染进程调用插件进程的处理器
	 */
	callHandler(handlerName: string, data: any): Promise<any>;

	/**
	 * 从渲染进程发送消息到插件进程
	 */
	postMessage(message: any): Promise<void>;

	/**
	 * 监听来自插件进程的事件
	 */
	onMessage: Event<BridgeMessage>;
}

// 定义 IBridgeService 接口
export interface IBridgeService {
	readonly _serviceBrand: undefined;

	/**
	 * 调用处理器
	 * @param handlerName 处理器名称
	 * @param data 数据
	 * @param callback 回调函数
	 */
	callHandler(handlerName: string, data: any, callback?: CallbackFunction): void;

	/**
	 * 注册处理器
	 * @param name 处理器名称
	 * @param handler 处理器函数
	 */
	registerHandler(name: string, handler: HandlerFunction): void;

	/**
	 * 注册桥接命令
	 * @param commandName 命令名称
	 * @param onHandler 处理器函数
	 */
	reigisterBridgeCommand(commandName: string, onHandler: HandlerFunction): void;

	/**
	 * 发送消息
	 * @param message 消息内容
	 */
	postMessage(message: any): void;

	/**
	 * 添加消息监听器
	 * @param listener 监听器函数
	 */
	addMessageListener(listener: MessageListener): void;

	/**
	 * 移除消息监听器
	 * @param listener 监听器函数
	 */
	removeMessageListener(listener: MessageListener): void;

	/**
	 * 清理资源
	 */
	dispose(): void;
}

// 创建服务标识符
export const IBridgeService = createDecorator<IBridgeService>('bridgeService');
