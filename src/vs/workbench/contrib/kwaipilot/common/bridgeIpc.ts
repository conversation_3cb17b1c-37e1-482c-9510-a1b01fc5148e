/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Event, Emitter } from '../../../../base/common/event.js';
import { IChannel, IServerChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { BridgeMessage, IKwaiPilotBridgeChannel, HandlerFunction } from './bridgeService.js';
import { CancellationToken } from '../../../../base/common/cancellation.js';

/**
 * KwaiPilot Bridge IPC 服务端通道 - 运行在插件进程中
 */
export class KwaiPilotBridgeServerChannel implements IServerChannel {
	private handlers = new Map<string, HandlerFunction>();
	private readonly _onMessage = new Emitter<BridgeMessage>();
	readonly onMessage: Event<BridgeMessage> = this._onMessage.event;

	constructor() {}

	listen(_: unknown, event: string): Event<any> {
		switch (event) {
			case 'onMessage': return this.onMessage;
		}
		throw new Error(`Event not found: ${event}`);
	}

	async call(_: unknown, command: string, arg?: any, cancellationToken?: CancellationToken): Promise<any> {
		switch (command) {
			case 'callHandler': {
				const { handlerName, data } = arg;
				const handler = this.handlers.get(handlerName);
				if (!handler) {
					throw new Error(`Handler not found: ${handlerName}`);
				}
				return await handler(data);
			}
			case 'postMessage': {
				const message = arg;
				this._onMessage.fire(message);
				return;
			}
		}
		throw new Error(`Call not found: ${command}`);
	}

	/**
	 * 注册处理器
	 */
	registerHandler(name: string, handler: HandlerFunction): void {
		this.handlers.set(name, handler);
	}

	/**
	 * 发送消息到渲染进程
	 */
	sendMessage(message: BridgeMessage): void {
		this._onMessage.fire(message);
	}

	dispose(): void {
		this.handlers.clear();
		this._onMessage.dispose();
	}
}

/**
 * KwaiPilot Bridge IPC 客户端通道 - 运行在渲染进程中
 */
export class KwaiPilotBridgeClientChannel implements IKwaiPilotBridgeChannel {
	readonly onMessage: Event<BridgeMessage>;

	constructor(private channel: IChannel) {
		this.onMessage = this.channel.listen<BridgeMessage>('onMessage');
	}

	async callHandler(handlerName: string, data: any): Promise<any> {
		return this.channel.call('callHandler', { handlerName, data });
	}

	async postMessage(message: any): Promise<void> {
		return this.channel.call('postMessage', message);
	}
}
