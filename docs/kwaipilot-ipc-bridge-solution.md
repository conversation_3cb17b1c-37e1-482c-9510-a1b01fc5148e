# KwaiPilot IPC Bridge 解决方案

## 问题背景

当前 KwaiPilot 的渲染进程和插件进程通信使用 `executeCommand` 方式，但该方式会经过 RPC 的 `deserializeRequestJSONArgs` 序列化，导致 ArrayBuffer 等二进制数据丢失。

## 解决方案概述

使用 VSCode 的 IPC 通道机制替代 `executeCommand`，支持二进制数据传输。

## 架构设计

```
渲染进程 (BridgeImpl.ts)     ←→     插件进程 (vscodeNativeView.ts)
        ↓                                    ↓
  IPC Client Channel                 IPC Server Channel
        ↓                                    ↓
    IChannel                           IServerChannel
        ↓                                    ↓
  Extension Host Connection    ←→    Extension Host Protocol
```

## 实现方案

### 1. 创建 IPC 通道接口

**文件**: `src/vs/workbench/contrib/kwaipilot/common/kwaiPilotIpc.ts`

```typescript
import { Event } from '../../../../base/common/event.js';
import { IChannel, IServerChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { CancellationToken } from '../../../../base/common/cancellation.js';

export interface IKwaiPilotBridgeService {
  /**
   * 调用插件进程的处理器
   */
  callHandler(handlerName: string, data: any): Promise<any>;

  /**
   * 发送单向消息到插件进程
   */
  postMessage(message: any): Promise<void>;

  /**
   * 监听来自插件进程的消息
   */
  onMessage: Event<any>;
}

export const IKwaiPilotBridgeService = Symbol('IKwaiPilotBridgeService');
```

### 2. 插件进程 IPC 服务端实现

**文件**: `src/vs/kwaipilot/src/services/kwaiPilotIpcServer.ts`

```typescript
import { Event, Emitter } from 'vs/base/common/event';
import { IServerChannel } from 'vs/base/parts/ipc/common/ipc';
import { CancellationToken } from 'vs/base/common/cancellation';
import { ContextManager } from '../base/context-manager';
import { Bridge } from '@bridge';

export class KwaiPilotBridgeServerChannel implements IServerChannel {
  private readonly _onMessage = new Emitter<any>();
  readonly onMessage: Event<any> = this._onMessage.event;

  constructor(private context: ContextManager) {}

  listen(_: unknown, event: string): Event<any> {
    switch (event) {
      case 'onMessage': return this.onMessage;
    }
    throw new Error(`Event not found: ${event}`);
  }

  async call(_: unknown, command: string, arg?: any, cancellationToken?: CancellationToken): Promise<any> {
    switch (command) {
      case 'callHandler': {
        const { handlerName, data, callbackId } = arg;
        return new Promise((resolve, reject) => {
          try {
            // 调用现有的 Bridge 处理逻辑
            this.context.getService(Bridge).callNativeHandler(
              null, // webview 参数，在 IPC 模式下可以为 null
              handlerName,
              data,
              callbackId,
              (response: any) => resolve(response)
            );
          } catch (error) {
            reject(error);
          }
        });
      }

      case 'postMessage': {
        const message = arg;
        // 处理单向消息
        this.context.getService(Bridge).handleOneWayMessage(null, message);
        return;
      }
    }
    throw new Error(`Call not found: ${command}`);
  }

  /**
   * 发送消息到渲染进程
   */
  sendMessageToRenderer(message: any): void {
    this._onMessage.fire(message);
  }

  dispose(): void {
    this._onMessage.dispose();
  }
}
```

### 3. 渲染进程 IPC 客户端实现

**文件**: `src/vs/workbench/contrib/kwaipilot/browser/kwaiPilotIpcClient.ts`

```typescript
import { Event } from '../../../../base/common/event.js';
import { IChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { IKwaiPilotBridgeService } from '../common/kwaiPilotIpc.js';

export class KwaiPilotBridgeClientService implements IKwaiPilotBridgeService {
  readonly onMessage: Event<any>;

  constructor(private channel: IChannel) {
    this.onMessage = this.channel.listen<any>('onMessage');
  }

  async callHandler(handlerName: string, data: any): Promise<any> {
    const callbackId = Math.random().toString(36).substr(2, 9);
    return this.channel.call('callHandler', { handlerName, data, callbackId });
  }

  async postMessage(message: any): Promise<void> {
    return this.channel.call('postMessage', message);
  }
}
```

### 4. 修改渲染进程 BridgeImpl.ts

```typescript
import { IExtensionService } from '../../../services/extensions/common/extensions.js';
import { KwaiPilotBridgeClientService } from './kwaiPilotIpcClient.js';

export class BridgeService extends Disposable implements IBridgeService {
  private kwaiPilotIpcService: KwaiPilotBridgeClientService | undefined;

  constructor(
    @ICommandService private readonly commandService: ICommandService,
    @IExtensionService private readonly extensionService: IExtensionService
  ) {
    super();
    this.initializeIpcConnection();
  }

  private async initializeIpcConnection(): Promise<void> {
    try {
      // 获取到插件进程的 IPC 连接
      const extensionHostManager = (this.extensionService as any)._extensionHostManagers?.[0];
      if (extensionHostManager && extensionHostManager._rpcProtocol) {
        const channel = extensionHostManager._rpcProtocol.getChannel('kwaiPilotBridge');
        this.kwaiPilotIpcService = new KwaiPilotBridgeClientService(channel);

        // 监听来自插件进程的消息
        this.kwaiPilotIpcService.onMessage(message => {
          this.handleMessageFromExtension(message);
        });
      }
    } catch (error) {
      console.warn('Failed to initialize IPC connection, falling back to command-based communication', error);
    }
  }

  callHandler(handlerName: string, data: any, callback?: CallbackFunction): void {
    if (this.kwaiPilotIpcService) {
      // 使用 IPC 通道
      this.kwaiPilotIpcService.callHandler(handlerName, data)
        .then(response => callback?.(response))
        .catch(error => console.error('IPC call failed:', error));
    } else {
      // 回退到命令方式
      const callbackId = this.callbackId++;
      if (callback) {
        this.callbacks.set(callbackId, callback);
      }
      this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', {
        protocol: 'callHandler',
        name: handlerName,
        callbackId,
        data,
      });
    }
  }

  postMessage(message: any): void {
    if (this.kwaiPilotIpcService) {
      this.kwaiPilotIpcService.postMessage(message);
    } else {
      this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', {
        protocol: 'message',
        data: message,
      });
    }
  }
}
```

### 5. 修改插件进程 vscodeNativeView.ts

```typescript
import { KwaiPilotBridgeServerChannel } from '../services/kwaiPilotIpcServer';

export class Webview extends BaseModule {
  private ipcServerChannel: KwaiPilotBridgeServerChannel;

  constructor(ext: ContextManager) {
    super(ext);

    // 初始化 IPC 服务端
    this.ipcServerChannel = new KwaiPilotBridgeServerChannel(ext);

    // 注册 IPC 通道到扩展主机
    this.registerIpcChannel();

    // 保留命令注册作为回退方案
    this.registerCommandHandlers();
  }

  private registerIpcChannel(): void {
    try {
      // 注册 IPC 通道
      // 这需要访问扩展主机的 RPC 协议
      const rpcProtocol = (global as any).__vscode_extension_host_rpc_protocol__;
      if (rpcProtocol) {
        rpcProtocol.set('kwaiPilotBridge', this.ipcServerChannel);
      }
    } catch (error) {
      console.warn('Failed to register IPC channel:', error);
    }
  }

  private registerCommandHandlers(): void {
    // 保留原有的命令处理逻辑作为回退
    vscode.commands.registerCommand(
      "kwaipilot.bridge.postMessageFromUI",
      (message) => {
        // 现有的处理逻辑...
      }
    );
  }

  // 发送消息到渲染进程
  sendMessageToRenderer(message: any): void {
    if (this.ipcServerChannel) {
      this.ipcServerChannel.sendMessageToRenderer(message);
    } else {
      // 回退到命令方式
      vscode.commands.executeCommand('kwaipilot.bridge.postMessageFromExtension', message);
    }
  }
}
```

## 优势

1. **支持二进制数据**: IPC 通道原生支持 ArrayBuffer 传输
2. **性能更好**: 避免了 JSON 序列化/反序列化开销
3. **类型安全**: 可以传输复杂对象结构
4. **向后兼容**: 保留命令方式作为回退方案

## 注意事项

1. 需要确保扩展主机连接已建立
2. 错误处理和重连机制
3. 生命周期管理和资源清理
4. 测试覆盖 IPC 和命令两种模式

## 实现步骤

### 第一步：创建 IPC 接口和通道
1. 创建 `src/vs/workbench/contrib/kwaipilot/common/kwaiPilotIpc.ts`
2. 创建 `src/vs/kwaipilot/src/services/kwaiPilotIpcServer.ts`
3. 创建 `src/vs/workbench/contrib/kwaipilot/browser/kwaiPilotIpcClient.ts`

### 第二步：修改现有代码
1. 修改 `src/vs/workbench/contrib/kwaipilot/browser/BridgeImpl.ts`
2. 修改 `src/vs/kwaipilot/src/base/webview/vscodeNativeView.ts`

### 第三步：注册 IPC 通道
1. 在插件进程启动时注册服务端通道
2. 在渲染进程中获取客户端通道

### 第四步：测试验证
1. 创建二进制数据传输测试
2. 验证回退机制工作正常
3. 性能对比测试

## 关键代码片段

### 获取扩展主机 RPC 协议

```typescript
// 在渲染进程中获取扩展主机连接
private async getExtensionHostRpcProtocol(): Promise<any> {
  const extensionService = this.extensionService as any;

  // 等待扩展主机启动
  await extensionService.whenInstalledExtensionsRegistered();

  // 获取扩展主机管理器
  const extensionHostManagers = extensionService._extensionHostManagers;
  if (!extensionHostManagers || extensionHostManagers.length === 0) {
    throw new Error('No extension host managers found');
  }

  const extensionHostManager = extensionHostManagers[0];
  const rpcProtocol = await extensionHostManager._proxy;

  if (!rpcProtocol || !rpcProtocol._rpcProtocol) {
    throw new Error('Extension host RPC protocol not available');
  }

  return rpcProtocol._rpcProtocol;
}
```

### 在插件进程中注册通道

```typescript
// 在插件进程中注册 IPC 通道
export function registerKwaiPilotIpcChannel(context: vscode.ExtensionContext, contextManager: ContextManager) {
  const serverChannel = new KwaiPilotBridgeServerChannel(contextManager);

  // 获取扩展主机的 RPC 协议
  const rpcProtocol = (global as any).__vscode_extension_host_rpc_protocol__;
  if (rpcProtocol) {
    rpcProtocol.set('kwaiPilotBridge', serverChannel);
    console.log('KwaiPilot IPC channel registered successfully');
  } else {
    console.warn('Extension host RPC protocol not available, falling back to command-based communication');
  }

  // 清理资源
  context.subscriptions.push({
    dispose: () => serverChannel.dispose()
  });
}
```

### 二进制数据传输示例

```typescript
// 发送包含 ArrayBuffer 的数据
const binaryData = {
  text: 'Hello World',
  buffer: new ArrayBuffer(1024),
  typedArray: new Uint8Array([1, 2, 3, 4, 5])
};

bridgeService.callHandler('processBinaryData', binaryData, (response) => {
  console.log('Received response with preserved binary data:', response);
  console.log('ArrayBuffer preserved:', response.buffer instanceof ArrayBuffer);
  console.log('TypedArray preserved:', response.typedArray instanceof Uint8Array);
});
```

## 错误处理和回退机制

```typescript
class BridgeService {
  private async sendWithFallback(method: string, ...args: any[]): Promise<any> {
    if (this.kwaiPilotIpcService) {
      try {
        return await (this.kwaiPilotIpcService as any)[method](...args);
      } catch (error) {
        console.warn(`IPC ${method} failed, falling back to command:`, error);
        this.kwaiPilotIpcService = undefined; // 禁用 IPC，使用命令回退
      }
    }

    // 回退到命令方式
    return this.sendViaCommand(method, ...args);
  }
}
```

## 性能优化建议

1. **连接池管理**: 复用 IPC 连接，避免频繁建立连接
2. **批量传输**: 对于大量小消息，考虑批量发送
3. **压缩传输**: 对于大型数据，可以考虑压缩
4. **缓存机制**: 缓存频繁访问的数据，减少 IPC 调用

这个方案完全替代了 `executeCommand` 方式，支持二进制数据传输，并且保持了良好的向后兼容性。
