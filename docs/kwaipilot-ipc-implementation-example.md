# KwaiPilot IPC 实现示例

## 核心文件创建

### 1. IPC 接口定义

**文件**: `src/vs/workbench/contrib/kwaipilot/common/kwaiPilotIpc.ts`

```typescript
import { Event } from '../../../../base/common/event.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';

export interface IKwaiPilotBridgeService {
  callHandler(handlerName: string, data: any): Promise<any>;
  postMessage(message: any): Promise<void>;
  onMessage: Event<any>;
}

export const IKwaiPilotBridgeService = createDecorator<IKwaiPilotBridgeService>('kwaiPilotBridgeService');
```

### 2. 插件进程服务端

**文件**: `src/vs/kwaipilot/src/services/kwaiPilotIpcServer.ts`

```typescript
import { Event, Emitter } from 'vs/base/common/event';
import { IServerChannel } from 'vs/base/parts/ipc/common/ipc';
import { CancellationToken } from 'vs/base/common/cancellation';
import { ContextManager } from '../base/context-manager';
import { Bridge } from '@bridge';

export class KwaiPilotBridgeServerChannel implements IServerChannel {
  private readonly _onMessage = new Emitter<any>();
  readonly onMessage: Event<any> = this._onMessage.event;
  
  constructor(private context: ContextManager) {}

  listen(_: unknown, event: string): Event<any> {
    switch (event) {
      case 'onMessage': return this.onMessage;
    }
    throw new Error(`Event not found: ${event}`);
  }

  async call(_: unknown, command: string, arg?: any, cancellationToken?: CancellationToken): Promise<any> {
    const bridge = this.context.getService(Bridge);
    
    switch (command) {
      case 'callHandler': {
        const { handlerName, data, callbackId } = arg;
        
        return new Promise((resolve, reject) => {
          try {
            // 使用现有的 Bridge 逻辑，但修改回调处理
            const originalCallback = (response: any) => resolve(response);
            
            bridge.callNativeHandler(
              null, // webview 在 IPC 模式下可以为 null
              handlerName,
              data,
              callbackId,
              originalCallback
            );
          } catch (error) {
            reject(error);
          }
        });
      }
      
      case 'postMessage': {
        const message = arg;
        bridge.handleOneWayMessage(null, message);
        return;
      }
    }
    
    throw new Error(`Call not found: ${command}`);
  }

  sendMessageToRenderer(message: any): void {
    this._onMessage.fire(message);
  }

  dispose(): void {
    this._onMessage.dispose();
  }
}
```

### 3. 渲染进程客户端

**文件**: `src/vs/workbench/contrib/kwaipilot/browser/kwaiPilotIpcClient.ts`

```typescript
import { Event } from '../../../../base/common/event.js';
import { IChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { IKwaiPilotBridgeService } from '../common/kwaiPilotIpc.js';

export class KwaiPilotBridgeClientService implements IKwaiPilotBridgeService {
  readonly onMessage: Event<any>;

  constructor(private channel: IChannel) {
    this.onMessage = this.channel.listen<any>('onMessage');
  }

  async callHandler(handlerName: string, data: any): Promise<any> {
    const callbackId = Math.random().toString(36).substr(2, 9);
    return this.channel.call('callHandler', { handlerName, data, callbackId });
  }

  async postMessage(message: any): Promise<void> {
    return this.channel.call('postMessage', message);
  }
}
```

### 4. 修改 BridgeImpl.ts

```typescript
// 在 BridgeImpl.ts 中添加
import { IExtensionService } from '../../../services/extensions/common/extensions.js';
import { KwaiPilotBridgeClientService } from './kwaiPilotIpcClient.js';

export class BridgeService extends Disposable implements IBridgeService {
  private kwaiPilotIpcService: KwaiPilotBridgeClientService | undefined;

  constructor(
    @ICommandService private readonly commandService: ICommandService,
    @IExtensionService private readonly extensionService: IExtensionService
  ) {
    super();
    this.initializeIpcConnection();
    // ... 其他初始化代码
  }

  private async initializeIpcConnection(): Promise<void> {
    try {
      // 等待扩展服务准备就绪
      await this.extensionService.whenInstalledExtensionsRegistered();
      
      // 获取扩展主机管理器
      const extensionService = this.extensionService as any;
      const extensionHostManagers = extensionService._extensionHostManagers;
      
      if (extensionHostManagers && extensionHostManagers.length > 0) {
        const extensionHostManager = extensionHostManagers[0];
        const proxy = await extensionHostManager._proxy;
        
        if (proxy && proxy._rpcProtocol) {
          const channel = proxy._rpcProtocol.getChannel('kwaiPilotBridge');
          this.kwaiPilotIpcService = new KwaiPilotBridgeClientService(channel);
          
          // 监听来自插件进程的消息
          this.kwaiPilotIpcService.onMessage(message => {
            this.handleMessageFromExtension(message);
          });
          
          console.log('KwaiPilot IPC connection established');
        }
      }
    } catch (error) {
      console.warn('Failed to initialize IPC connection:', error);
    }
  }

  callHandler(handlerName: string, data: any, callback?: CallbackFunction): void {
    if (this.kwaiPilotIpcService) {
      // 使用 IPC 通道 - 支持二进制数据
      this.kwaiPilotIpcService.callHandler(handlerName, data)
        .then(response => callback?.(response))
        .catch(error => {
          console.error('IPC call failed:', error);
          // 可以选择回退到命令方式
          this.fallbackToCommand(handlerName, data, callback);
        });
    } else {
      // 回退到命令方式
      this.fallbackToCommand(handlerName, data, callback);
    }
  }

  private fallbackToCommand(handlerName: string, data: any, callback?: CallbackFunction): void {
    const callbackId = this.callbackId++;
    if (callback) {
      this.callbacks.set(callbackId, callback);
    }
    
    this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', {
      protocol: 'callHandler',
      name: handlerName,
      callbackId,
      data,
    });
  }
}
```

### 5. 修改 vscodeNativeView.ts

```typescript
// 在 vscodeNativeView.ts 中添加
import { KwaiPilotBridgeServerChannel } from '../services/kwaiPilotIpcServer';

export class Webview extends BaseModule {
  private ipcServerChannel: KwaiPilotBridgeServerChannel;

  constructor(ext: ContextManager) {
    super(ext);
    
    // 初始化 IPC 服务端
    this.ipcServerChannel = new KwaiPilotBridgeServerChannel(ext);
    this.registerIpcChannel();
    
    // 保留原有命令注册作为回退
    this.registerCommandHandlers();
  }

  private registerIpcChannel(): void {
    try {
      // 获取扩展主机的 RPC 协议
      const rpcProtocol = (global as any).__vscode_extension_host_rpc_protocol__;
      if (rpcProtocol) {
        rpcProtocol.set('kwaiPilotBridge', this.ipcServerChannel);
        console.log('KwaiPilot IPC server channel registered');
      } else {
        console.warn('Extension host RPC protocol not available');
      }
    } catch (error) {
      console.warn('Failed to register IPC channel:', error);
    }
  }

  private registerCommandHandlers(): void {
    // 保留原有的命令处理逻辑
    vscode.commands.registerCommand(
      "kwaipilot.bridge.postMessageFromUI",
      (message) => {
        // 现有的处理逻辑...
      }
    );
  }

  // 发送消息到渲染进程
  sendMessageToRenderer(message: any): void {
    if (this.ipcServerChannel) {
      this.ipcServerChannel.sendMessageToRenderer(message);
    } else {
      // 回退到命令方式
      vscode.commands.executeCommand('kwaipilot.bridge.postMessageFromExtension', message);
    }
  }
}
```

## 使用示例

```typescript
// 发送包含 ArrayBuffer 的数据
const binaryData = {
  text: 'Hello World',
  buffer: new ArrayBuffer(1024),
  typedArray: new Uint8Array([1, 2, 3, 4, 5])
};

// 通过 IPC 发送，ArrayBuffer 会被保留
bridgeService.callHandler('processBinaryData', binaryData, (response) => {
  console.log('ArrayBuffer preserved:', response.buffer instanceof ArrayBuffer);
  console.log('TypedArray preserved:', response.typedArray instanceof Uint8Array);
});
```

这个实现方案完全替代了 `executeCommand`，支持二进制数据传输，并保持了向后兼容性。
